import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Container,
  Group,
  LoadingOverlay,
  Paper,
  ScrollArea,
  Stack,
  Text,
  Textarea,
  Title,
} from '@mantine/core';
import { useForm } from 'react-hook-form';

import { Bot, Send, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import { streamAgentQuery, StreamEvent } from '../api/agentStreamApi';

interface FormData {
  query: string;
}

interface Message {
  id: string;
  type: 'user' | 'agent' | 'event';
  content: string;
  timestamp: Date;
  author?: string;
  eventType?: string;
  isError?: boolean;
  isFinal?: boolean;
}

export default function AgentSpace() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const form = useForm<FormData>({
    defaultValues: {
      query: '',
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, newMessage]);
  };

  const stopStreaming = () => {
    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
    setIsStreaming(false);
  };

  const handleSubmit = async (data: FormData) => {
    if (isStreaming) {
      stopStreaming();
      return;
    }

    const query = data.query.trim();
    if (!query) return;

    // Add user message
    addMessage({
      type: 'user',
      content: query,
    });

    // Clear the form
    form.reset({ query: '' });

    setIsStreaming(true);

    try {
      await streamAgentQuery(query, {
        onEvent: (event: StreamEvent) => {
          if (event.content) {
            addMessage({
              type: 'agent',
              content: event.content,
              author: event.author,
              eventType: event.type,
              isFinal: event.is_final,
            });
          }
        },
        onError: (error: Error) => {
          console.error('Streaming error:', error);
          addMessage({
            type: 'event',
            content: `Error: ${error.message}`,
            isError: true,
            isFinal: true,
          });
          console.error('Connection Error: Failed to connect to the AI Agent. Please try later.');
          setIsStreaming(false);
        },
        onComplete: () => {
          setIsStreaming(false);
        },
      });
    } catch (error) {
      console.error('Streaming setup error:', error);
      addMessage({
        type: 'event',
        content: `Setup Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isError: true,
        isFinal: true,
      });
      setIsStreaming(false);
    }
  };

  const getMessageIcon = (message: Message) => {
    if (message.type === 'user') return null;
    if (message.isError) return <AlertCircle size={16} className="text-red-500" />;
    if (message.isFinal) return <CheckCircle size={16} className="text-green-500" />;
    return <Clock size={16} className="text-blue-500" />;
  };

  const getMessageBadgeColor = (message: Message) => {
    if (message.isError) return 'red';
    if (message.isFinal) return 'green';
    return 'blue';
  };

  return (
    <Container fluid p="md" className="h-full">
      <Stack gap="md" className="h-full">
        {/* Header */}
        <Paper p="lg" radius="md" withBorder>
          <Group gap="md">
            <Bot size={32} className="text-blue-600" />
            <div>
              <Title order={2}>Abilytics AI Agent</Title>
              <Text size="sm" c="dimmed">
                Your AI Teammate
              </Text>
            </div>
          </Group>
        </Paper>

        {/* Messages Area */}
        <Card className="flex-1 flex flex-col" p="md">
          <ScrollArea className="flex-1 mb-4" scrollbarSize={8}>
            <Stack gap="md">
              {messages.length === 0 && (
                <Alert icon={<Bot size={16} />} title="Welcome!" color="blue">
                  Describe your incident or system issue, and I'll guide you through a comprehensive resolution process.
                </Alert>
              )}
              
              {messages.map((message) => (
                <Paper
                  key={message.id}
                  p="md"
                  radius="md"
                  className={`${
                    message.type === 'user' 
                      ? 'bg-blue-50 border-blue-200 ml-8' 
                      : 'bg-gray-50 border-gray-200 mr-8'
                  } border`}
                >
                  <Group gap="xs" mb="xs">
                    {getMessageIcon(message)}
                    <Text size="sm" fw={500}>
                      {message.type === 'user' ? 'You' : message.author || 'Agent'}
                    </Text>
                    {message.eventType && (
                      <Badge size="xs" color={getMessageBadgeColor(message)}>
                        {message.eventType}
                      </Badge>
                    )}
                    <Text size="xs" c="dimmed">
                      {message.timestamp.toLocaleTimeString()}
                    </Text>
                  </Group>
                  <Text size="sm" style={{ whiteSpace: 'pre-wrap' }}>
                    {message.content}
                  </Text>
                </Paper>
              ))}
              
              {isStreaming && (
                <Paper p="md" radius="md" className="bg-yellow-50 border-yellow-200 border mr-8">
                  <Group gap="xs">
                    <LoadingOverlay visible={false} />
                    <Clock size={16} className="text-yellow-600 animate-pulse" />
                    <Text size="sm" fw={500}>Processing...</Text>
                  </Group>
                  <Text size="sm" c="dimmed">
                    Abilytics AI Agent is analyzing your request...
                  </Text>
                </Paper>
              )}
            </Stack>
            <div ref={messagesEndRef} />
          </ScrollArea>

          {/* Input Form */}
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <Group gap="md" align="flex-end">
              <Textarea
                {...form.register('query', {
                  required: 'Please enter a query',
                  validate: (value) => value.trim().length > 0 || 'Please enter a query'
                })}
                placeholder="Describe your incident or system issue..."
                disabled={isStreaming}
                autosize
                minRows={2}
                maxRows={4}
                className="flex-1"
                error={form.formState.errors.query?.message}
              />
              <Button
                type="submit"
                loading={isStreaming}
                leftSection={isStreaming ? undefined : <Send size={16} />}
                color={isStreaming ? 'red' : 'blue'}
              >
                {isStreaming ? 'Stop' : 'Send'}
              </Button>
            </Group>
          </form>
        </Card>
      </Stack>
    </Container>
  );
}
