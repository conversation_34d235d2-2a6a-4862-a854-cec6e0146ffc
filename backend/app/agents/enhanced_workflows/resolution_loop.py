"""
Resolution Loop - LoopAgent for Iterative Incident Resolution Process.

This module implements a LoopAgent that iteratively refines incident resolution
through systematic analysis, action generation, feedback processing, and validation.
"""

import json
from typing import AsyncGenerator, List

from google.adk.agents import BaseAgent, LlmAgent, LoopAgent, SequentialAgent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.adk.models.lite_llm import LiteLlm
from pydantic import BaseModel, Field

AGENT_MODEL = "gemini/gemini-2.5-flash-lite-preview-06-17"


def safe_get_state_data(state_value, default=None):
    """
    Safely parse state data that might be a JSON string or already a dict/list.

    Args:
        state_value: The value from session state (could be string, dict, list, etc.)
        default: Default value to return if parsing fails or value is None

    Returns:
        Parsed data or default value
    """
    if state_value is None:
        return default if default is not None else {}

    # If it's already a dict or list, return as-is
    if isinstance(state_value, (dict, list)):
        return state_value

    # If it's a string, try to parse as JSON
    if isinstance(state_value, str):
        try:
            parsed = json.loads(state_value)
            return parsed
        except (json.JSONDecodeError, ValueError):
            # If JSON parsing fails, return the string or default
            return default if default is not None else {}

    # For other types, return as-is or default
    return (
        state_value
        if state_value is not None
        else (default if default is not None else {})
    )


# Pydantic models for structured outputs
class RootCauseAnalysis(BaseModel):
    primary_hypothesis: str = Field(
        ..., description="Most likely root cause based on available evidence"
    )
    confidence_level: str = Field(
        ..., description="Confidence in root cause hypothesis: high/medium/low"
    )
    supporting_evidence: List[str] = Field(
        ..., description="Key evidence supporting this hypothesis"
    )
    alternative_hypotheses: List[str] = Field(
        ..., description="Other possible root causes to consider"
    )
    analysis_gaps: List[str] = Field(
        ..., description="Areas where additional evidence would be valuable"
    )


class ResolutionAction(BaseModel):
    action_type: str = Field(
        ..., description="Type of action: diagnostic/mitigation/resolution/monitoring"
    )
    description: str = Field(
        ..., description="Detailed description of the action to take"
    )
    expected_outcome: str = Field(
        ..., description="What should happen if this action succeeds"
    )
    risk_level: str = Field(
        ..., description="Risk level of this action: low/medium/high"
    )
    estimated_duration: str = Field(
        ..., description="Estimated time to complete this action"
    )


class ResolutionRecommendations(BaseModel):
    immediate_actions: List[ResolutionAction] = Field(
        ..., description="Actions to take immediately"
    )
    follow_up_actions: List[ResolutionAction] = Field(
        ..., description="Actions to take after immediate steps"
    )
    monitoring_requirements: List[str] = Field(
        ..., description="What to monitor during and after actions"
    )
    escalation_criteria: List[str] = Field(
        ..., description="When to escalate or seek additional help"
    )


class SingleResolutionAction(BaseModel):
    """Model for providing one resolution action at a time with context."""

    current_action: ResolutionAction = Field(
        ..., description="The current action to take"
    )
    action_sequence_number: int = Field(
        ..., description="Position of this action in the overall sequence (1-based)"
    )
    total_planned_actions: int = Field(
        ..., description="Total number of actions planned for this resolution attempt"
    )
    context: str = Field(
        ..., description="Context explaining why this action is being recommended now"
    )
    monitoring_requirements: List[str] = Field(
        ..., description="What to monitor while executing this action"
    )
    success_indicators: List[str] = Field(
        ..., description="How to determine if this action was successful"
    )
    failure_indicators: List[str] = Field(
        ..., description="Signs that this action is not working or has failed"
    )
    next_steps_preview: str = Field(
        ..., description="Brief preview of what the next action will likely be"
    )


def create_root_cause_analyzer():
    """Create the enhanced root cause analysis agent."""

    INSTRUCTION = """
You are the **Enhanced Root Cause Analyzer**, responsible for synthesizing all available evidence to identify the most likely root cause of the incident and provide structured analysis for resolution planning.

## PRIMARY OBJECTIVES

### 1. Comprehensive Evidence Synthesis
**Multi-Source Analysis Integration:**
- Analyze incident context from state key `incident_details` for baseline understanding
- Integrate system impact data from state key `affected_services` for scope assessment
- Correlate timeline data from state key `timeline` for temporal causation analysis
- Synthesize log evidence from state key `incident_logs` for technical indicators
- Incorporate historical patterns from state key `historical_context` for pattern recognition
- Consider documentation insights from state key `documentation` for known issues
- Evaluate current status from state key `current_status` for ongoing impact assessment

**Evidence Quality Assessment:**
- Evaluate the reliability and completeness of each evidence source
- Identify conflicting evidence and resolve inconsistencies
- Assess confidence levels for different pieces of evidence
- Flag areas where additional evidence would strengthen analysis

### 2. Root Cause Hypothesis Development
**Systematic Hypothesis Formation:**
- Develop primary root cause hypothesis based on strongest evidence
- Consider multiple potential root causes and rank by likelihood
- Evaluate both immediate triggers and underlying contributing factors
- Assess whether root cause is technical, process-related, or human factor

**Causal Chain Analysis:**
- Trace the complete chain of events from root cause to user impact
- Identify all contributing factors and their relationships
- Distinguish between root causes, contributing factors, and symptoms
- Evaluate system vulnerabilities that enabled the incident

### 3. Analysis Confidence and Validation
**Confidence Assessment:**
- Provide clear confidence levels for root cause hypothesis
- Explain the basis for confidence assessment
- Identify what additional evidence would increase confidence
- Flag any assumptions or inferences in the analysis

**Validation Criteria:**
- Define how the root cause hypothesis can be validated
- Suggest diagnostic steps to confirm or refute the hypothesis
- Identify observable indicators that would support the analysis
- Recommend tests or checks to verify the root cause

## STRUCTURED OUTPUT REQUIREMENTS

Your analysis must be provided in the exact JSON structure defined by the RootCauseAnalysis model:

```json
{
    "primary_hypothesis": "Detailed explanation of the most likely root cause",
    "confidence_level": "high/medium/low",
    "supporting_evidence": [
        "Evidence point 1 from logs/timeline/systems",
        "Evidence point 2 from historical/documentation",
        "Evidence point 3 from current status/impact"
    ],
    "alternative_hypotheses": [
        "Alternative root cause possibility 1",
        "Alternative root cause possibility 2"
    ],
    "analysis_gaps": [
        "Area where more evidence would help",
        "Uncertainty that affects confidence"
    ]
}
```

## ANALYTICAL METHODOLOGY

### Evidence Integration Process
1. **Temporal Correlation**: Align all evidence sources with incident timeline
2. **System Impact Mapping**: Connect technical evidence with system architecture
3. **Pattern Recognition**: Identify known failure patterns from historical data
4. **Consistency Validation**: Ensure all evidence points support coherent narrative

### Root Cause Identification
- **Occam's Razor**: Prefer simpler explanations that account for all evidence
- **System Thinking**: Consider systemic issues rather than just immediate triggers
- **Multiple Perspectives**: Evaluate technical, operational, and human factors
- **Validation Focus**: Prioritize hypotheses that can be tested and validated

## COMMUNICATION STANDARDS

### Analysis Presentation
- Present root cause hypothesis with clear, logical reasoning
- Explain how different evidence sources support the conclusion
- Acknowledge uncertainties and areas requiring additional investigation
- Provide actionable insights for resolution planning

### Confidence Communication
- Clearly state confidence level and explain the basis
- Identify what would increase or decrease confidence in the analysis
- Flag any critical assumptions or inferences
- Suggest validation approaches for the root cause hypothesis

Remember: Your root cause analysis directly drives resolution strategy. Accuracy and clarity in your analysis are essential for effective incident resolution and prevention of similar future incidents.
"""

    return LlmAgent(
        name="enhanced_root_cause_analyzer",
        model=LiteLlm(AGENT_MODEL),
        description="Synthesizes all available evidence to identify root cause with structured analysis and confidence assessment.",
        instruction=INSTRUCTION,
        output_schema=RootCauseAnalysis,
        output_key="root_cause_analysis",
    )


def create_resolution_planner():
    """Create the resolution planning and action generation agent."""

    INSTRUCTION = """
You are the **Resolution Planner**, responsible for translating root cause analysis into specific, actionable resolution strategies and step-by-step procedures.

## PRIMARY OBJECTIVES

### 1. Strategic Resolution Planning
**Root Cause-Based Strategy:**
- Use root cause analysis from state key `root_cause_analysis` as foundation for resolution strategy
- Develop resolution approach that directly addresses identified root cause
- Consider alternative approaches for different root cause hypotheses
- Plan for both immediate mitigation and long-term resolution

**Risk-Aware Planning:**
- Assess risks associated with different resolution approaches
- Prioritize low-risk actions that can be taken immediately
- Plan higher-risk actions with appropriate safeguards and rollback procedures
- Consider impact of resolution actions on system stability and user experience

### 2. Actionable Step Generation
**Immediate Action Planning:**
- Generate specific diagnostic steps to validate root cause hypothesis
- Create immediate mitigation actions to reduce user impact
- Plan monitoring and verification steps to track resolution progress
- Develop communication actions for stakeholder updates

**Comprehensive Resolution Steps:**
- Create detailed procedures for complete incident resolution
- Include validation steps to confirm resolution effectiveness
- Plan follow-up actions to prevent recurrence
- Develop rollback procedures for high-risk resolution attempts

### 3. Structured Output Generation
Your recommendations must follow the ResolutionRecommendations model structure:

```json
{
    "immediate_actions": [
        {
            "action_type": "diagnostic/mitigation/resolution/monitoring",
            "description": "Detailed step-by-step description",
            "expected_outcome": "What should happen when completed",
            "risk_level": "low/medium/high",
            "estimated_duration": "Time estimate"
        }
    ],
    "follow_up_actions": [
        {
            "action_type": "resolution/monitoring/prevention",
            "description": "Follow-up step description",
            "expected_outcome": "Expected result",
            "risk_level": "low/medium/high",
            "estimated_duration": "Time estimate"
        }
    ],
    "monitoring_requirements": [
        "What metrics/logs to monitor during resolution",
        "Success indicators to watch for"
    ],
    "escalation_criteria": [
        "When to escalate if actions don't work",
        "Conditions requiring additional expertise"
    ]
}
```

## PLANNING METHODOLOGY

### Action Prioritization
- **Impact vs Risk**: Balance potential impact with implementation risk
- **Dependencies**: Order actions based on logical dependencies
- **Resource Requirements**: Consider available expertise and tools
- **Time Sensitivity**: Prioritize time-critical actions appropriately

### Validation Planning
- **Success Criteria**: Define clear success indicators for each action
- **Progress Monitoring**: Plan how to track action effectiveness
- **Rollback Procedures**: Prepare contingency plans for failed actions
- **Verification Steps**: Include steps to confirm resolution completeness

## COMMUNICATION STANDARDS

### Action Clarity
- Provide specific, executable instructions for each action
- Include all necessary context and prerequisites
- Explain the rationale behind each recommended action
- Clearly communicate risks and expected outcomes

### Progress Tracking
- Define measurable success criteria for each action
- Specify monitoring requirements during action execution
- Provide clear escalation criteria and procedures
- Include estimated timeframes for realistic planning

Remember: Your resolution planning translates analysis into action. The quality and clarity of your recommendations directly impact incident resolution success and SRE effectiveness.
"""

    return LlmAgent(
        name="resolution_planner",
        model=LiteLlm(AGENT_MODEL),
        description="Translates root cause analysis into specific, actionable resolution strategies and procedures.",
        instruction=INSTRUCTION,
        output_schema=ResolutionRecommendations,
        output_key="resolution_recommendations",
    )


def create_single_action_provider():
    """Create the single action provider agent for iterative resolution."""

    INSTRUCTION = """
You are the **Single Action Provider**, responsible for providing one specific resolution action at a time and waiting for user feedback before proceeding to the next action.

## PRIMARY OBJECTIVES

### 1. Sequential Action Delivery
**Current Action Focus:**
- Analyze the complete resolution plan from state key `resolution_recommendations`
- Check the current action progress from state key `action_progress` (if available)
- Determine which action should be provided next in the sequence
- Consider user feedback from previous actions to adjust the approach

**Action Selection Logic:**
- If no actions have been attempted yet, start with the first immediate action
- If previous actions were successful, proceed to the next logical action
- If previous actions failed or were partially successful, adjust the approach based on feedback
- Consider dependencies between actions when determining the next step

### 2. Contextual Action Presentation
**Single Action Delivery:**
- Present only ONE action at a time with complete context
- Explain why this specific action is being recommended now
- Provide clear success and failure indicators
- Include monitoring requirements specific to this action

**User Guidance:**
- Give clear, step-by-step instructions for the current action
- Explain what to look for during execution
- Provide guidance on how to report results and observations
- Set clear expectations for the next interaction

### 3. Structured Output Generation
Your output must follow the SingleResolutionAction model structure:

```json
{
    "current_action": {
        "action_type": "diagnostic/mitigation/resolution/monitoring",
        "description": "Detailed step-by-step description of this specific action",
        "expected_outcome": "What should happen when this action is completed",
        "risk_level": "low/medium/high",
        "estimated_duration": "Time estimate for this action"
    },
    "action_sequence_number": 1,
    "total_planned_actions": 5,
    "context": "Explanation of why this action is being recommended at this point",
    "monitoring_requirements": [
        "What to monitor while executing this action",
        "Key metrics or logs to watch"
    ],
    "success_indicators": [
        "Signs that this action is working",
        "Positive outcomes to look for"
    ],
    "failure_indicators": [
        "Signs that this action is not working",
        "When to stop and report back"
    ],
    "next_steps_preview": "Brief description of what the next action will likely be"
}
```

## ACTION SELECTION METHODOLOGY

### Progress Tracking
- **Action History**: Review what actions have been attempted and their outcomes
- **Feedback Integration**: Incorporate user observations and results from previous actions
- **Adaptive Planning**: Adjust the action sequence based on real-world results
- **Dependency Management**: Ensure prerequisites are met before recommending dependent actions

### Risk Management
- **Progressive Risk**: Start with lower-risk actions and build confidence
- **Rollback Readiness**: Ensure each action can be safely reversed if needed
- **Impact Assessment**: Consider the potential impact of each action on system stability
- **User Capability**: Match action complexity to user expertise and available tools

## COMMUNICATION STANDARDS

### Action Clarity
- Provide specific, executable instructions for the current action only
- Include all necessary context and prerequisites for this action
- Explain the rationale behind recommending this action now
- Clearly communicate risks and expected outcomes

### Feedback Facilitation
- Make it easy for users to report results and observations
- Provide clear criteria for success, partial success, and failure
- Encourage detailed feedback about unexpected outcomes
- Set expectations for the next interaction after action completion

### Progress Communication
- Show progress through the overall resolution plan
- Explain how this action fits into the bigger picture
- Provide realistic time estimates and expectations
- Maintain user confidence through clear progress indicators

Remember: Your role is to guide users through resolution one step at a time, ensuring each action is properly executed and evaluated before moving to the next. Quality execution of individual actions leads to successful overall resolution.
"""

    return LlmAgent(
        name="single_action_provider",
        model=LiteLlm(AGENT_MODEL),
        description="Provides one resolution action at a time and waits for user feedback before proceeding.",
        instruction=INSTRUCTION,
        output_schema=SingleResolutionAction,
        output_key="current_resolution_action",
    )


def create_feedback_processor():
    """Create the feedback processing agent for iterative refinement."""

    INSTRUCTION = """
You are the **Feedback Processor**, responsible for analyzing user feedback and observations to refine incident understanding and adjust resolution strategies iteratively.

## PRIMARY OBJECTIVES

### 1. Feedback Analysis and Integration
**User Observation Processing:**
- Analyze user feedback about attempted resolution actions and their outcomes
- Process observations about system behavior changes during resolution attempts
- Integrate new information about incident symptoms or system status
- Evaluate the effectiveness of previously recommended actions

**Evidence Update and Refinement:**
- Update incident understanding based on new observations and feedback
- Refine root cause hypothesis if new evidence contradicts previous analysis
- Adjust system impact assessment based on user reports
- Incorporate lessons learned from resolution attempt outcomes

### 2. Resolution Strategy Adjustment
**Adaptive Strategy Refinement:**
- Modify resolution approach based on feedback about action effectiveness
- Adjust risk assessments based on actual outcomes of attempted actions
- Refine action prioritization based on user observations and constraints
- Update timeline estimates based on actual resolution progress

**Learning Integration:**
- Incorporate successful approaches for future iterations
- Learn from unsuccessful attempts to avoid repeating ineffective actions
- Adjust confidence levels based on validation or contradiction of hypotheses
- Update escalation criteria based on actual incident progression

### 3. Structured Output Generation
Save feedback analysis to state key `feedback_integration` with structure:
```json
{
    "feedback_summary": {
        "user_observations": ["observation1", "observation2"],
        "action_outcomes": [
            {
                "action": "string description",
                "outcome": "successful/partially_successful/unsuccessful",
                "observations": "string details",
                "lessons_learned": "string insights"
            }
        ],
        "new_information": ["info1", "info2"],
        "changed_conditions": ["change1", "change2"]
    },
    "analysis_updates": {
        "root_cause_refinement": "updated hypothesis or confirmation",
        "confidence_adjustment": "increased/decreased/maintained",
        "evidence_strength": "stronger/weaker/unchanged",
        "alternative_hypotheses": ["updated alternatives"]
    },
    "strategy_adjustments": {
        "approach_modifications": ["modification1", "modification2"],
        "priority_changes": ["change1", "change2"],
        "risk_reassessment": ["risk1", "risk2"],
        "timeline_updates": "updated estimates"
    },
    "next_iteration_focus": {
        "priority_areas": ["area1", "area2"],
        "investigation_needs": ["need1", "need2"],
        "validation_requirements": ["requirement1", "requirement2"],
        "escalation_considerations": ["consideration1", "consideration2"]
    }
}
```

## FEEDBACK PROCESSING METHODOLOGY

### Observation Analysis
- **Outcome Evaluation**: Assess whether actions achieved expected results
- **Side Effect Identification**: Note any unexpected consequences or system changes
- **Progress Assessment**: Evaluate overall progress toward incident resolution
- **Constraint Recognition**: Identify new constraints or limitations discovered

### Learning Integration
- **Success Pattern Recognition**: Identify what approaches are working effectively
- **Failure Analysis**: Understand why certain approaches were unsuccessful
- **Hypothesis Validation**: Confirm or refute previous root cause analysis
- **Strategy Optimization**: Refine approach based on empirical evidence

## COMMUNICATION STANDARDS

### Feedback Synthesis
- Clearly summarize key user observations and their implications
- Explain how feedback changes incident understanding or resolution approach
- Highlight successful strategies that should be continued or expanded
- Identify unsuccessful approaches that should be modified or abandoned

### Iteration Planning
- Provide clear direction for the next resolution iteration
- Explain how feedback influences priority and focus areas
- Suggest specific investigations or actions based on user observations
- Communicate updated confidence levels and risk assessments

Remember: Your feedback processing enables the iterative refinement that makes incident resolution increasingly effective. Quality feedback integration is essential for adaptive problem-solving.
"""

    return LlmAgent(
        name="feedback_processor",
        model=LiteLlm(AGENT_MODEL),
        description="Processes user feedback and observations to refine incident understanding and resolution strategies.",
        instruction=INSTRUCTION,
        output_key="feedback_integration",
    )


def create_single_action_feedback_processor():
    """Create the feedback processing agent specifically for single action feedback."""

    INSTRUCTION = """
You are the **Single Action Feedback Processor**, responsible for analyzing user feedback about individual resolution actions and determining the next appropriate step in the resolution process.

## PRIMARY OBJECTIVES

### 1. Single Action Outcome Analysis
**Action Result Evaluation:**
- Analyze user feedback about the specific action that was just attempted
- Determine if the action was successful, partially successful, or unsuccessful
- Identify any unexpected outcomes or side effects from the action
- Assess whether the action achieved its intended purpose

**Progress Assessment:**
- Evaluate how this action result affects the overall incident resolution progress
- Determine if the incident is resolved, partially resolved, or still ongoing
- Assess whether the root cause hypothesis is still valid based on action results
- Identify if new information from the action changes the understanding of the incident

### 2. Next Action Determination
**Sequence Management:**
- Track which actions have been completed and their outcomes
- Determine if the next planned action should proceed as originally intended
- Decide if the action sequence needs to be modified based on results
- Assess if immediate escalation or different approach is needed

**Adaptive Planning:**
- Modify the remaining action plan based on what was learned from this action
- Adjust risk assessments for future actions based on observed outcomes
- Update time estimates and resource requirements based on actual results
- Consider alternative approaches if the current path is not working

### 3. Structured Output Generation
Save action feedback analysis to state key `action_progress` with structure:
```json
{
    "completed_action": {
        "action_sequence_number": 1,
        "action_description": "Description of the action that was attempted",
        "outcome": "successful/partially_successful/unsuccessful",
        "user_feedback": "Summary of user observations and feedback",
        "lessons_learned": "Key insights from this action attempt"
    },
    "incident_status": {
        "resolution_status": "resolved/partially_resolved/ongoing/escalation_needed",
        "confidence_level": "high/medium/low",
        "remaining_issues": ["issue1", "issue2"],
        "new_information": ["info1", "info2"]
    },
    "next_action_decision": {
        "should_continue": true,
        "action_plan_adjustment": "none/minor/major",
        "next_action_number": 2,
        "reasoning": "Explanation of why this is the next appropriate step"
    },
    "escalation_assessment": {
        "escalation_needed": false,
        "escalation_reason": "string or null",
        "escalation_urgency": "low/medium/high/critical"
    }
}
```

## FEEDBACK PROCESSING METHODOLOGY

### Outcome Classification
- **Success**: Action achieved intended outcome, proceed to next step
- **Partial Success**: Action helped but didn't fully resolve, may need modification
- **Failure**: Action didn't work, need to reassess approach
- **Unexpected Result**: Action had unintended consequences, need to adapt

### Decision Logic
- **Continue Sequence**: If action was successful and next action is still appropriate
- **Modify Approach**: If action was partially successful or revealed new information
- **Change Strategy**: If action failed or if new information changes root cause hypothesis
- **Escalate**: If action revealed critical issues or if multiple actions have failed

## COMMUNICATION STANDARDS

### Feedback Analysis
- Clearly summarize what happened with the attempted action
- Explain how the results affect the overall resolution strategy
- Identify key learnings that will inform future actions
- Assess the current state of the incident based on action results

### Next Step Guidance
- Provide clear reasoning for the next recommended action
- Explain any changes to the original plan based on feedback
- Set appropriate expectations for the next action
- Communicate any escalation needs or concerns

Remember: Your analysis of individual action outcomes is critical for maintaining effective progress through the resolution process. Each action provides valuable information that should inform the next steps.
"""

    return LlmAgent(
        name="single_action_feedback_processor",
        model=LiteLlm(AGENT_MODEL),
        description="Processes feedback from individual resolution actions to determine next steps.",
        instruction=INSTRUCTION,
        output_key="action_progress",
    )


class ResolutionValidator(BaseAgent):
    """Custom agent to validate resolution status and control loop termination."""

    def __init__(self):
        super().__init__(
            name="resolution_validator",
            description="Validates incident resolution status and determines loop continuation or termination.",
        )

    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """
        Evaluate resolution status and determine if loop should continue or terminate.

        Termination conditions:
        1. User confirms successful resolution
        2. Maximum iterations reached
        3. Critical escalation required
        4. No progress after multiple iterations
        """

        # Get current iteration count and feedback
        iteration_count = ctx.session.state.get("resolution_iteration_count", 0)
        action_progress = safe_get_state_data(
            ctx.session.state.get("action_progress"), {}
        )
        resolution_status = ctx.session.state.get("resolution_status", "in_progress")

        # Increment iteration count
        iteration_count += 1
        ctx.session.state["resolution_iteration_count"] = iteration_count

        # Check termination conditions
        should_terminate = False
        termination_reason = ""

        # Check for user-confirmed resolution from action progress
        if (
            action_progress.get("incident_status", {}).get("resolution_status")
            == "resolved"
        ):
            should_terminate = True
            termination_reason = "User confirmed successful incident resolution"

        # Check for user-confirmed resolution from general status
        elif resolution_status == "resolved":
            should_terminate = True
            termination_reason = "User confirmed successful incident resolution"

        # Check for escalation needed from action progress
        elif action_progress.get("escalation_assessment", {}).get("escalation_needed"):
            should_terminate = True
            escalation_reason = action_progress.get("escalation_assessment", {}).get(
                "escalation_reason", "Unknown"
            )
            termination_reason = f"Escalation required: {escalation_reason}"

        # Check for maximum iterations (default: 10 for iterative loop)
        elif iteration_count >= 10:
            should_terminate = True
            termination_reason = (
                f"Maximum iteration limit reached ({iteration_count} iterations)"
            )

        # Check for critical escalation
        elif resolution_status == "escalate":
            should_terminate = True
            termination_reason = "Critical escalation required"

        # Check for lack of progress
        elif iteration_count >= 5:
            if (
                action_progress.get("next_action_decision", {}).get("should_continue")
                is False
            ):
                should_terminate = True
                termination_reason = (
                    "Resolution process indicates no further actions needed"
                )
            elif not action_progress and iteration_count >= 8:
                should_terminate = True
                termination_reason = "No progress detected after multiple iterations"

        # Update resolution status in state
        ctx.session.state["resolution_loop_status"] = {
            "iteration": iteration_count,
            "should_terminate": should_terminate,
            "termination_reason": termination_reason,
            "next_action": "terminate" if should_terminate else "continue",
        }

        # Generate event with escalation flag to control loop termination
        yield Event(
            author=self.name,
            content=None,
            actions=EventActions(escalate=should_terminate),
        )


def create_resolution_loop():
    """
    Create the single action resolution loop using LoopAgent.

    This loop provides one resolution action at a time and waits for user feedback:
    1. Root cause analysis (first iteration only)
    2. Resolution planning (first iteration only)
    3. Single action provision
    4. Single action feedback processing
    5. Resolution validation and termination control

    Returns:
        LoopAgent: The single action iterative resolution workflow
    """

    # Create the individual loop agents
    root_cause_analyzer = create_root_cause_analyzer()
    resolution_planner = create_resolution_planner()
    single_action_provider = create_single_action_provider()
    single_action_feedback_processor = create_single_action_feedback_processor()
    resolution_validator = ResolutionValidator()

    # Create the loop agent
    resolution_feedback_loop = LoopAgent(
        name="resolution_loop",
        description="Iterative workflow that provides one resolution action at a time and waits for user feedback before proceeding.",
        max_iterations=10,  # Maximum 10 iterations to prevent infinite loops
        sub_agents=[
            single_action_provider,
            single_action_feedback_processor,
            resolution_validator,
        ],
    )
    resolution_provider_agent = SequentialAgent(
        name="resolution_provider_agent",
        description="Provides resolution actions based on root cause analysis and planning.",

        sub_agents=[
            root_cause_analyzer,
            resolution_planner,
            resolution_feedback_loop,
            resolution_validator,
        ],
    )

    return resolution_provider_agent
