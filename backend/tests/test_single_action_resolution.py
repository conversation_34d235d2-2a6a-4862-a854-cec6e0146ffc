"""
Test suite for the single action resolution functionality.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from agents.enhanced_workflows.resolution_loop import (
    SingleResolutionAction,
    ResolutionAction,
    create_single_action_provider,
    create_single_action_feedback_processor,
    create_single_action_resolution_loop,
)


class TestSingleResolutionActionModel:
    """Test the SingleResolutionAction Pydantic model."""

    def test_single_resolution_action_creation(self):
        """Test creating a SingleResolutionAction instance."""
        action = ResolutionAction(
            action_type="diagnostic",
            description="Check system logs for errors",
            expected_outcome="Identify error patterns",
            risk_level="low",
            estimated_duration="5 minutes"
        )
        
        single_action = SingleResolutionAction(
            current_action=action,
            action_sequence_number=1,
            total_planned_actions=3,
            context="Starting with diagnostic to understand the issue",
            monitoring_requirements=["Watch error logs", "Monitor system metrics"],
            success_indicators=["Error patterns identified", "No new errors"],
            failure_indicators=["No logs available", "System unresponsive"],
            next_steps_preview="Next we will apply mitigation based on findings"
        )
        
        assert single_action.current_action.action_type == "diagnostic"
        assert single_action.action_sequence_number == 1
        assert single_action.total_planned_actions == 3
        assert len(single_action.monitoring_requirements) == 2
        assert len(single_action.success_indicators) == 2
        assert len(single_action.failure_indicators) == 2

    def test_single_resolution_action_serialization(self):
        """Test that SingleResolutionAction can be serialized to dict."""
        action = ResolutionAction(
            action_type="mitigation",
            description="Restart affected service",
            expected_outcome="Service comes back online",
            risk_level="medium",
            estimated_duration="2 minutes"
        )
        
        single_action = SingleResolutionAction(
            current_action=action,
            action_sequence_number=2,
            total_planned_actions=4,
            context="Service restart based on diagnostic findings",
            monitoring_requirements=["Service health check"],
            success_indicators=["Service is running"],
            failure_indicators=["Service fails to start"],
            next_steps_preview="Verify service functionality"
        )
        
        action_dict = single_action.model_dump()
        
        assert action_dict["action_sequence_number"] == 2
        assert action_dict["current_action"]["action_type"] == "mitigation"
        assert "monitoring_requirements" in action_dict
        assert "success_indicators" in action_dict


class TestSingleActionAgents:
    """Test the single action agent creation functions."""

    def test_create_single_action_provider(self):
        """Test creating the single action provider agent."""
        agent = create_single_action_provider()
        
        assert agent.name == "single_action_provider"
        assert agent.output_key == "current_resolution_action"
        assert "Single Action Provider" in agent.instruction
        assert agent.output_schema == SingleResolutionAction

    def test_create_single_action_feedback_processor(self):
        """Test creating the single action feedback processor agent."""
        agent = create_single_action_feedback_processor()
        
        assert agent.name == "single_action_feedback_processor"
        assert agent.output_key == "action_progress"
        assert "Single Action Feedback Processor" in agent.instruction

    def test_create_single_action_resolution_loop(self):
        """Test creating the single action resolution loop."""
        loop_agent = create_single_action_resolution_loop()
        
        assert loop_agent.name == "single_action_resolution_loop"
        assert loop_agent.max_iterations == 20
        assert len(loop_agent.sub_agents) == 5  # 5 sub-agents in the loop
        
        # Check that all expected agents are present
        agent_names = [agent.name for agent in loop_agent.sub_agents]
        expected_names = [
            "enhanced_root_cause_analyzer",
            "resolution_planner", 
            "single_action_provider",
            "single_action_feedback_processor",
            "resolution_validator"
        ]
        
        for expected_name in expected_names:
            assert expected_name in agent_names


class TestSingleActionWorkflow:
    """Test the single action workflow logic."""

    def test_action_sequence_logic(self):
        """Test that action sequence numbers work correctly."""
        # Simulate first action
        first_action = SingleResolutionAction(
            current_action=ResolutionAction(
                action_type="diagnostic",
                description="Check logs",
                expected_outcome="Find errors",
                risk_level="low",
                estimated_duration="5 min"
            ),
            action_sequence_number=1,
            total_planned_actions=3,
            context="Starting diagnosis",
            monitoring_requirements=["Log monitoring"],
            success_indicators=["Errors found"],
            failure_indicators=["No logs"],
            next_steps_preview="Apply fix"
        )
        
        assert first_action.action_sequence_number == 1
        assert first_action.total_planned_actions == 3
        
        # Simulate second action
        second_action = SingleResolutionAction(
            current_action=ResolutionAction(
                action_type="mitigation",
                description="Apply fix",
                expected_outcome="Issue resolved",
                risk_level="medium",
                estimated_duration="10 min"
            ),
            action_sequence_number=2,
            total_planned_actions=3,
            context="Applying fix based on diagnosis",
            monitoring_requirements=["System health"],
            success_indicators=["System stable"],
            failure_indicators=["System unstable"],
            next_steps_preview="Verify resolution"
        )
        
        assert second_action.action_sequence_number == 2
        assert second_action.current_action.action_type == "mitigation"

    def test_progress_tracking_structure(self):
        """Test the action progress tracking structure."""
        # This would typically be created by the feedback processor
        action_progress = {
            "completed_action": {
                "action_sequence_number": 1,
                "action_description": "Check system logs",
                "outcome": "successful",
                "user_feedback": "Found error patterns in logs",
                "lessons_learned": "Error occurs during peak traffic"
            },
            "incident_status": {
                "resolution_status": "ongoing",
                "confidence_level": "medium",
                "remaining_issues": ["Need to fix root cause"],
                "new_information": ["Traffic spike correlation"]
            },
            "next_action_decision": {
                "should_continue": True,
                "action_plan_adjustment": "minor",
                "next_action_number": 2,
                "reasoning": "Diagnostic successful, proceed with mitigation"
            },
            "escalation_assessment": {
                "escalation_needed": False,
                "escalation_reason": None,
                "escalation_urgency": "low"
            }
        }
        
        # Verify structure
        assert action_progress["completed_action"]["outcome"] == "successful"
        assert action_progress["incident_status"]["resolution_status"] == "ongoing"
        assert action_progress["next_action_decision"]["should_continue"] is True
        assert action_progress["escalation_assessment"]["escalation_needed"] is False


if __name__ == "__main__":
    pytest.main([__file__])
